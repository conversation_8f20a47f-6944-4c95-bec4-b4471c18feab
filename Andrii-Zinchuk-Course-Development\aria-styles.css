/* AI Coach ARIA - Advanced Career Transformation Platform */
/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Particles Background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 25px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.voice-toggle {
    display: flex;
    align-items: center;
}

.voice-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.voice-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

/* Main Container */
.main-container {
    display: flex;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

.content-area {
    flex: 1;
    padding: 40px;
    margin-right: 400px; /* Space for ARIA sidebar */
}

/* Section Management */
.section {
    display: none;
    animation: fadeInUp 0.6s ease forwards;
}

.section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.section-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.section-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
}

.dashboard-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.dashboard-card h3 {
    color: #333;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
}

.dashboard-card h3 i {
    color: #4a90e2;
    font-size: 1.5rem;
}

/* Progress Gauge */
.progress-gauge {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto 30px;
}

.gauge-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.gauge-value {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.gauge-label {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.progress-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 8px;
}

.detail-item span:first-child {
    font-weight: 600;
    color: #555;
}

.detail-item span:last-child {
    color: #4a90e2;
    font-weight: 500;
}

/* Skills Radar */
.skills-legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-item.current .legend-color {
    background: #4a90e2;
}

.legend-item.target .legend-color {
    background: #ff6b6b;
}

/* Salary Details */
.salary-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.salary-current,
.salary-target,
.salary-growth {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
}

.salary-current {
    background: rgba(108, 117, 125, 0.1);
}

.salary-target {
    background: rgba(74, 144, 226, 0.1);
}

.salary-growth {
    background: rgba(46, 204, 113, 0.1);
}

.salary-details .label {
    font-weight: 600;
    color: #555;
}

.salary-details .value {
    font-weight: bold;
    color: #333;
}

.salary-details .value.positive {
    color: #2ecc71;
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-align: center;
}

.action-btn i {
    font-size: 1.5rem;
}

.action-btn.primary {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.action-btn.tertiary {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.action-btn.quaternary {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Achievements List */
.achievements-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    border-radius: 12px;
    animation: achievementSlideIn 0.5s ease;
}

.achievement-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.achievement-content h4 {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.achievement-content p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.view-all-btn {
    width: 100%;
    padding: 12px;
    background: transparent;
    border: 2px solid #4a90e2;
    color: #4a90e2;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background: #4a90e2;
    color: white;
}

/* Timeline Preview */
.timeline-preview {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(74, 144, 226, 0.05);
    border-radius: 10px;
    border-left: 4px solid #4a90e2;
}

.timeline-date {
    background: #4a90e2;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
}

.timeline-content h4 {
    color: #333;
    margin-bottom: 5px;
}

.timeline-content p {
    color: #666;
    font-size: 0.9rem;
}

/* ARIA Sidebar */
.aria-sidebar {
    position: fixed;
    right: 0;
    top: 70px;
    width: 400px;
    height: calc(100vh - 70px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    box-shadow: -5px 0 30px rgba(0, 0, 0, 0.1);
}

.aria-header {
    padding: 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.aria-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4a90e2, #667eea);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.aria-info h3 {
    color: #333;
    margin-bottom: 5px;
}

.aria-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.aria-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    color: #2ecc71;
    font-weight: 600;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #2ecc71;
    animation: pulse 2s infinite;
}

/* Chat Interface */
.aria-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chat-message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    animation: messageSlideIn 0.3s ease;
}

.chat-message.aria {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    align-self: flex-start;
    border-bottom-left-radius: 6px;
}

.chat-message.user {
    background: #f8f9fa;
    color: #333;
    align-self: flex-end;
    border-bottom-right-radius: 6px;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 5px;
}

/* Quick Replies */
.quick-replies {
    padding: 15px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.quick-reply-btn {
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
    border: 1px solid rgba(74, 144, 226, 0.3);
    padding: 8px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.quick-reply-btn:hover {
    background: #4a90e2;
    color: white;
}

/* Chat Input */
.chat-input-container {
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-input {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 5px;
}

.chat-input input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 16px;
    font-size: 0.9rem;
    outline: none;
}

.send-btn,
.voice-input-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.send-btn {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
}

.voice-input-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.send-btn:hover,
.voice-input-btn:hover {
    transform: scale(1.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
    padding: 25px;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff6b6b;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Button Styles */
.btn-primary,
.btn-secondary {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(45deg, #4a90e2, #667eea);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e0e0e0;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes achievementSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-area {
        margin-right: 0;
        padding: 20px;
    }
    
    .aria-sidebar {
        display: none;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .section-header h1 {
        font-size: 2rem;
    }
    
    .dashboard-card {
        padding: 20px;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
}
