/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Particles Background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #4a90e2;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #4a90e2;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat {
    text-align: center;
    color: white;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.cta-button {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.cta-button i {
    margin-right: 10px;
}

/* Profile Card */
.profile-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.3s ease;
}

.profile-card:hover {
    transform: perspective(1000px) rotateY(0deg);
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin-bottom: 20px;
    border: 5px solid #4a90e2;
}

.profile-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 10px;
}

.profile-card p {
    color: #666;
    margin-bottom: 20px;
}

.skills-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.skill-tag {
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Dashboard Section */
.dashboard {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: white;
    margin-bottom: 60px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.card-header {
    margin-bottom: 20px;
}

.card-header h3 {
    color: #333;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header i {
    color: #4a90e2;
}

/* Profile Section */
.profile-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
}

.profile-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.info-card h3 {
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-card i {
    color: #4a90e2;
}

.info-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.certification-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(74, 144, 226, 0.1);
    border-radius: 8px;
}

.cert-item i {
    color: #4a90e2;
}

/* Skills Analysis */
.skills-grid {
    display: grid;
    gap: 30px;
}

.skill-category h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.skill-bar {
    margin-bottom: 15px;
}

.skill-bar span {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.bar {
    background: #e0e0e0;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.fill {
    height: 100%;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    border-radius: 4px;
    transition: width 1s ease;
}

/* Courses Section */
.courses-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
}

.course-filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border-color: rgba(255, 255, 255, 0.9);
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

/* Progress Section */
.progress-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
}

.progress-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
}

.progress-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.progress-card h3 {
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.achievements-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.achievement {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
    border-radius: 10px;
    font-weight: 500;
}

/* Roadmap Section */
.roadmap-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.1);
}

.roadmap-container {
    max-width: 800px;
    margin: 0 auto;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 15px;
    color: #4a90e2;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    font-style: italic;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .profile-grid {
        grid-template-columns: 1fr;
    }
    
    .progress-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        display: none;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-card,
.info-card,
.progress-card {
    animation: fadeInUp 0.6s ease forwards;
}

/* Hover Effects */
.dashboard-card:hover,
.info-card:hover,
.progress-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}
