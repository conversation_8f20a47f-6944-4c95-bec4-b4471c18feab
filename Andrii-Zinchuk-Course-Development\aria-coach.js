// AI Coach ARIA - Step 1: Basic Navigation and Initialization
// Enhanced Career Transformation Platform for <PERSON><PERSON><PERSON>

// Global state management
let currentSection = 'dashboard';
let ariaConversationHistory = [];
let userProgress = {
    careerProgress: 35,
    completedCourses: 2,
    totalCourses: 5,
    achievements: [],
    currentGoals: []
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    console.log('✅ ARIA platform ready!');
});

// Initialize particle background
function initializeParticles() {
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            particles: {
                number: { value: 60, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.4, random: false },
                size: { value: 3, random: true },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: "#ffffff",
                    opacity: 0.3,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 4,
                    direction: "none",
                    random: false,
                    straight: false,
                    out_mode: "out",
                    bounce: false
                }
            },
            interactivity: {
                detect_on: "canvas",
                events: {
                    onhover: { enable: true, mode: "repulse" },
                    onclick: { enable: true, mode: "push" },
                    resize: true
                },
                modes: {
                    repulse: { distance: 100, duration: 0.4 },
                    push: { particles_nb: 2 }
                }
            },
            retina_detect: true
        });
    }
}

// Setup navigation functionality
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.section');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSection = this.getAttribute('href').substring(1);

            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');

            // Show target section
            sections.forEach(section => {
                section.classList.remove('active');
                if (section.id === targetSection) {
                    section.classList.add('active');
                }
            });

            currentSection = targetSection;

            // Notify ARIA about section change
            notifyARIASectionChange(targetSection);

            console.log(`📍 Navigated to: ${targetSection}`);
        });
    });
}

// Initialize ARIA chat system
function initializeARIA() {
    console.log('🤖 Initializing AI Coach ARIA...');

    // Setup chat input
    const chatInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendButton');
    const voiceButton = document.getElementById('voiceButton');

    if (chatInput && sendButton) {
        // Send message on Enter key
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Send message on button click
        sendButton.addEventListener('click', sendMessage);
    }

    if (voiceButton) {
        voiceButton.addEventListener('click', toggleVoiceInput);
    }

    // Setup voice toggle in navbar
    const voiceToggle = document.getElementById('voiceToggle');
    if (voiceToggle) {
        voiceToggle.addEventListener('click', openVoiceModal);
    }

    // Initialize with welcome message
    setTimeout(() => {
        addARIAMessage("Cześć Andrii! 👋 Jestem ARIA, Twój osobisty AI Coach. Jestem tutaj, aby pomóc Ci w transformacji kariery z Manufacturing Engineering do AI/Data Science. Jak mogę Ci dziś pomóc?");
        showQuickReplies([
            "Pokaż mój postęp",
            "Zaplanuj naukę",
            "Networking events",
            "Aktualizuj portfolio"
        ]);
    }, 1000);
}

// Load user progress from localStorage
function loadUserProgress() {
    const savedProgress = localStorage.getItem('andriiProgress');
    if (savedProgress) {
        userProgress = { ...userProgress, ...JSON.parse(savedProgress) };
    }

    // Initialize with some sample achievements if none exist
    if (userProgress.achievements.length === 0) {
        userProgress.achievements = [
            {
                id: 1,
                title: "Python Fundamentals",
                description: "Ukończono podstawowy kurs Python",
                date: new Date().toISOString(),
                impact: "+10% Data Analysis Skills",
                icon: "fab fa-python"
            },
            {
                id: 2,
                title: "RPA Professional",
                description: "Zdobyto certyfikat SS&C Blue Prism",
                date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                impact: "+15% Automation Skills",
                icon: "fas fa-robot"
            }
        ];
        saveUserProgress();
    }

    console.log('📊 User progress loaded:', userProgress);
}

// Save user progress to localStorage
function saveUserProgress() {
    localStorage.setItem('andriiProgress', JSON.stringify(userProgress));
}

// Send message to ARIA
function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();

    if (message) {
        addUserMessage(message);
        chatInput.value = '';

        // Process message with ARIA
        setTimeout(() => {
            processARIAResponse(message);
        }, 1000);
    }
}

// Add user message to chat
function addUserMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message user';
    messageElement.innerHTML = `
        ${message}
        <div class="message-time">${new Date().toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Add to conversation history
    ariaConversationHistory.push({
        type: 'user',
        message: message,
        timestamp: new Date().toISOString()
    });
}

// Add ARIA message to chat
function addARIAMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message aria';
    messageElement.innerHTML = `
        ${message}
        <div class="message-time">${new Date().toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Add to conversation history
    ariaConversationHistory.push({
        type: 'aria',
        message: message,
        timestamp: new Date().toISOString()
    });
}

// Show quick reply buttons
function showQuickReplies(replies) {
    const quickReplies = document.getElementById('quickReplies');
    quickReplies.innerHTML = '';

    replies.forEach(reply => {
        const button = document.createElement('button');
        button.className = 'quick-reply-btn';
        button.textContent = reply;
        button.addEventListener('click', () => {
            addUserMessage(reply);
            setTimeout(() => {
                processARIAResponse(reply);
            }, 1000);
        });
        quickReplies.appendChild(button);
    });
}

// Process ARIA response based on user input
function processARIAResponse(userMessage) {
    const message = userMessage.toLowerCase();
    let response = "";
    let quickReplies = [];

    if (message.includes('postęp') || message.includes('progress')) {
        response = `Świetnie! Twój obecny postęp w transformacji kariery wynosi ${userProgress.careerProgress}%. Ukończyłeś ${userProgress.completedCourses} z ${userProgress.totalCourses} planowanych kursów. Ostatnio zdobyłeś certyfikat RPA Professional - to doskonały krok w kierunku automatyzacji!`;
        quickReplies = ["Pokaż szczegóły", "Następne kroki", "Aktualizuj postęp"];
    } else if (message.includes('nauka') || message.includes('kurs')) {
        response = "Doskonały pomysł! Na podstawie Twojego profilu w inżynierii materiałowej, polecam skupić się na kursach Machine Learning for Manufacturing. Masz już solidne podstawy w Python - czas przejść do poziomu intermediate!";
        quickReplies = ["Pokaż kursy", "Zaplanuj harmonogram", "Sprawdź certyfikacje"];
    } else if (message.includes('networking') || message.includes('wydarzenia')) {
        response = "Networking to kluczowy element transformacji kariery! W Polsce mamy świetne wydarzenia AI. Najbliższe to Global AI Bootcamp Kraków (7 marca) i miesięczne Trójmiasto AI Meetup. Chcesz, żebym pomógł Ci się przygotować?";
        quickReplies = ["Zobacz wydarzenia", "Przygotuj pitch", "LinkedIn strategy"];
    } else if (message.includes('portfolio')) {
        response = "Twoje portfolio to Twoja wizytówka! Z Twoim doświadczeniem w SMT/CBA i PVD, możesz stworzyć unikalne projekty łączące manufacturing z AI. Polecam zacząć od Computer Vision dla Quality Control - to będzie hit!";
        quickReplies = ["Pomysły na projekty", "GitHub setup", "Portfolio review"];
    } else {
        response = "Rozumiem! Jestem tutaj, aby pomóc Ci w każdym aspekcie transformacji kariery. Czy chcesz porozmawiać o konkretnym obszarze rozwoju?";
        quickReplies = ["Kursy i certyfikacje", "Networking", "Portfolio", "Planowanie kariery"];
    }

    addARIAMessage(response);
    showQuickReplies(quickReplies);
}

// Notify ARIA about section changes
function notifyARIASectionChange(section) {
    let contextMessage = "";

    switch(section) {
        case 'dashboard':
            contextMessage = "Widzę, że przeglądasz dashboard. Twój postęp wygląda świetnie! Czy chcesz omówić jakiś konkretny aspekt?";
            break;
        case 'courses':
            contextMessage = "Świetnie, że sprawdzasz kursy Coursera! Te 5 certyfikacji to doskonały plan dla Twojej transformacji. Którym chcesz zacząć?";
            break;
        case 'certifications':
            contextMessage = "Certyfikacje premium to inwestycja w przyszłość! AWS ML Specialty ma najlepszy ROI dla Twojego profilu. Chcesz omówić strategię?";
            break;
        case 'portfolio':
            contextMessage = "Portfolio to Twoja siła! Z doświadczeniem w manufacturing możesz stworzyć projekty, których nikt inny nie ma. Pomogę Ci je zaplanować!";
            break;
        case 'networking':
            contextMessage = "Networking w AI community to klucz do sukcesu! Wydarzenia w Polsce są świetne. Chcesz, żebym pomógł Ci się przygotować?";
            break;
        case 'roadmap':
            contextMessage = "Mapa kariery pokazuje Twoją drogę do sukcesu! 12 miesięcy to realny czas na transformację. Omówimy najbliższe kroki?";
            break;
    }

    if (contextMessage) {
        setTimeout(() => {
            addARIAMessage(contextMessage);
            showQuickReplies(["Tak, pomóż mi", "Pokaż szczegóły", "Inne pytanie"]);
        }, 2000);
    }
}

// Voice input functionality
function toggleVoiceInput() {
    console.log('🎤 Voice input toggled');
    // This will be implemented in next step
    showNotification('Funkcja głosowa będzie dostępna wkrótce!', 'info');
}

function openVoiceModal() {
    const voiceModal = document.getElementById('voiceModal');
    if (voiceModal) {
        voiceModal.style.display = 'block';
    }
}

function closeVoiceModal() {
    const voiceModal = document.getElementById('voiceModal');
    if (voiceModal) {
        voiceModal.style.display = 'none';
    }
}

// Utility function for notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Export functions for global access
window.sendMessage = sendMessage;
window.toggleVoiceInput = toggleVoiceInput;
window.openVoiceModal = openVoiceModal;
window.closeVoiceModal = closeVoiceModal;

// Step 2: Dashboard Charts and Action Buttons
// Initialize dashboard when DOM is ready
function initializeDashboard() {
    console.log('📊 Initializing dashboard charts...');

    createProgressGauge();
    createSkillsRadar();
    createSalaryChart();
    populateAchievements();
    populateTimelinePreview();

    console.log('✅ Dashboard initialized');
}

// Create progress gauge chart
function createProgressGauge() {
    const ctx = document.getElementById('progressGauge');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [userProgress.careerProgress, 100 - userProgress.careerProgress],
                backgroundColor: [
                    'linear-gradient(45deg, #4a90e2, #667eea)',
                    '#e9ecef'
                ],
                borderWidth: 0,
                cutout: '75%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: { enabled: false }
            }
        }
    });
}

// Create skills radar chart
function createSkillsRadar() {
    const ctx = document.getElementById('skillsRadar');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: [
                'Materials Science',
                'Python Programming',
                'Data Analysis',
                'Machine Learning',
                'Quality Engineering',
                'Business Intelligence',
                'Leadership',
                'Manufacturing'
            ],
            datasets: [{
                label: 'Obecny poziom',
                data: [95, 70, 65, 60, 90, 45, 70, 95],
                backgroundColor: 'rgba(74, 144, 226, 0.2)',
                borderColor: '#4a90e2',
                borderWidth: 2,
                pointBackgroundColor: '#4a90e2'
            }, {
                label: 'Poziom docelowy',
                data: [98, 90, 88, 85, 95, 80, 85, 98],
                backgroundColor: 'rgba(255, 107, 107, 0.2)',
                borderColor: '#ff6b6b',
                borderWidth: 2,
                pointBackgroundColor: '#ff6b6b'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20,
                        display: false
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    pointLabels: {
                        font: { size: 11 },
                        color: '#666'
                    }
                }
            }
        }
    });
}

// Create salary projection chart
function createSalaryChart() {
    const ctx = document.getElementById('salaryChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Obecnie', '6 miesięcy', '1 rok', '18 miesięcy', '2 lata'],
            datasets: [{
                label: 'Projekcja wynagrodzeń (EUR)',
                data: [70000, 85000, 105000, 140000, 160000],
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#4a90e2',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    min: 60000,
                    max: 180000,
                    ticks: {
                        callback: function(value) {
                            return '€' + (value / 1000) + 'k';
                        },
                        color: '#666',
                        font: { size: 11 }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#666',
                        font: { size: 11 }
                    },
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Populate achievements list
function populateAchievements() {
    const achievementsList = document.getElementById('achievementsList');
    if (!achievementsList) return;

    achievementsList.innerHTML = '';

    // Show last 3 achievements
    const recentAchievements = userProgress.achievements.slice(-3);

    recentAchievements.forEach(achievement => {
        const achievementElement = document.createElement('div');
        achievementElement.className = 'achievement-item';
        achievementElement.innerHTML = `
            <div class="achievement-icon">
                <i class="${achievement.icon}"></i>
            </div>
            <div class="achievement-content">
                <h4>${achievement.title}</h4>
                <p>${achievement.description}</p>
                <small>${achievement.impact}</small>
            </div>
        `;

        achievementElement.addEventListener('click', () => {
            showAchievementModal(achievement);
        });

        achievementsList.appendChild(achievementElement);
    });
}

// Populate timeline preview
function populateTimelinePreview() {
    const timelinePreview = document.getElementById('timelinePreview');
    if (!timelinePreview) return;

    const upcomingMilestones = [
        {
            date: '15 Sty',
            title: 'Machine Learning Specialization',
            description: 'Rozpoczęcie kursu Andrew Ng na Coursera'
        },
        {
            date: '1 Lut',
            title: 'Portfolio Project #1',
            description: 'Computer Vision dla SMT Quality Control'
        },
        {
            date: '15 Lut',
            title: 'AWS ML Specialty',
            description: 'Przygotowania do egzaminu certyfikacyjnego'
        }
    ];

    timelinePreview.innerHTML = '';

    upcomingMilestones.forEach(milestone => {
        const milestoneElement = document.createElement('div');
        milestoneElement.className = 'timeline-item';
        milestoneElement.innerHTML = `
            <div class="timeline-date">${milestone.date}</div>
            <div class="timeline-content">
                <h4>${milestone.title}</h4>
                <p>${milestone.description}</p>
            </div>
        `;
        timelinePreview.appendChild(milestoneElement);
    });
}

// Action button functions
function startCourse() {
    console.log('🎓 Starting course...');

    addARIAMessage("Świetnie! Widzę, że chcesz rozpocząć naukę. Na podstawie Twojego profilu polecam zacząć od Machine Learning Specialization Andrew Ng. To idealny kurs dla inżynierów przechodzących do AI!");

    showQuickReplies([
        "Zapisz mnie na kurs",
        "Pokaż harmonogram",
        "Inne opcje"
    ]);

    // Navigate to courses section
    setTimeout(() => {
        document.querySelector('[href="#courses"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji kursów...', 'info');
}

function updateProgress() {
    console.log('📈 Updating progress...');

    // Simulate progress update
    userProgress.careerProgress = Math.min(userProgress.careerProgress + 5, 100);
    saveUserProgress();

    // Add new achievement
    const newAchievement = {
        id: Date.now(),
        title: "Postęp Zaktualizowany",
        description: "Ręczna aktualizacja postępu kariery",
        date: new Date().toISOString(),
        impact: "+5% Career Progress",
        icon: "fas fa-chart-line"
    };

    userProgress.achievements.push(newAchievement);
    saveUserProgress();

    // Refresh dashboard
    populateAchievements();
    createProgressGauge();

    addARIAMessage(`Doskonale! Zaktualizowałem Twój postęp do ${userProgress.careerProgress}%. Każdy krok przybliża Cię do celu! 🎯`);

    showQuickReplies([
        "Pokaż szczegóły",
        "Następne kroki",
        "Zaplanuj naukę"
    ]);

    showNotification('Postęp zaktualizowany!', 'success');

    // Show achievement modal
    setTimeout(() => {
        showAchievementModal(newAchievement);
    }, 1500);
}

function scheduleNetworking() {
    console.log('👥 Scheduling networking...');

    addARIAMessage("Networking to klucz do sukcesu w AI! Zaplanujmy Twoje uczestnictwo w wydarzeniach. Polecam zacząć od Global AI Bootcamp Kraków - to świetna okazja do poznania community!");

    showQuickReplies([
        "Zapisz na wydarzenie",
        "Przygotuj elevator pitch",
        "LinkedIn strategy"
    ]);

    // Navigate to networking section
    setTimeout(() => {
        document.querySelector('[href="#networking"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji networking...', 'info');
}

function reviewPortfolio() {
    console.log('📁 Reviewing portfolio...');

    addARIAMessage("Twoje portfolio to Twoja wizytówka! Z doświadczeniem w SMT/CBA i PVD masz unikalne możliwości. Stwórzmy projekty, które pokażą Twoją ekspertyzę w manufacturing + AI!");

    showQuickReplies([
        "Pomysły na projekty",
        "GitHub optimization",
        "Portfolio review"
    ]);

    // Navigate to portfolio section
    setTimeout(() => {
        document.querySelector('[href="#portfolio"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji portfolio...', 'info');
}

function viewAllAchievements() {
    console.log('🏆 Viewing all achievements...');

    addARIAMessage(`Masz już ${userProgress.achievements.length} osiągnięć! To świetny postęp. Każde osiągnięcie przybliża Cię do celu transformacji kariery. Które z nich było dla Ciebie najważniejsze?`);

    const achievementTitles = userProgress.achievements.map(a => a.title).slice(-4);
    showQuickReplies(achievementTitles);

    showNotification(`Masz ${userProgress.achievements.length} osiągnięć!`, 'success');
}

// Achievement modal functions
function showAchievementModal(achievement) {
    const modal = document.getElementById('achievementModal');
    const details = document.getElementById('achievementDetails');
    const impact = document.getElementById('achievementImpact');
    const nextSteps = document.getElementById('nextSteps');

    if (!modal || !details || !impact || !nextSteps) return;

    details.innerHTML = `
        <div class="achievement-showcase">
            <div class="achievement-icon-large">
                <i class="${achievement.icon}"></i>
            </div>
            <h3>${achievement.title}</h3>
            <p>${achievement.description}</p>
            <div class="achievement-date">
                Osiągnięte: ${new Date(achievement.date).toLocaleDateString('pl-PL')}
            </div>
        </div>
    `;

    impact.innerHTML = `
        <h4><i class="fas fa-chart-line"></i> Wpływ na Rozwój</h4>
        <div class="impact-item">
            <span class="impact-label">Bezpośredni wpływ:</span>
            <span class="impact-value">${achievement.impact}</span>
        </div>
        <div class="impact-item">
            <span class="impact-label">Postęp kariery:</span>
            <span class="impact-value">+${Math.floor(Math.random() * 3 + 2)}% ogólny postęp</span>
        </div>
        <div class="impact-item">
            <span class="impact-label">Wartość rynkowa:</span>
            <span class="impact-value">+€${Math.floor(Math.random() * 5000 + 2000)} potencjał zarobków</span>
        </div>
    `;

    nextSteps.innerHTML = `
        <h4><i class="fas fa-arrow-right"></i> Następne Kroki</h4>
        <ul class="next-steps-list">
            <li>Zaktualizuj profil LinkedIn z nowym osiągnięciem</li>
            <li>Dodaj projekt do portfolio GitHub</li>
            <li>Podziel się sukcesem w AI community</li>
            <li>Zaplanuj następny milestone</li>
        </ul>
    `;

    modal.style.display = 'block';
}

function closeAchievementModal() {
    const modal = document.getElementById('achievementModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function continueWithARIA() {
    closeAchievementModal();
    addARIAMessage("Gratulacje ponownie! 🎉 Jestem dumny z Twojego postępu. Czy chcesz omówić następne kroki w Twojej transformacji kariery?");
    showQuickReplies([
        "Zaplanuj następny cel",
        "Aktualizuj LinkedIn",
        "Nowy projekt portfolio"
    ]);
}

// Update the initialization to include dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    // Initialize dashboard after a short delay to ensure DOM is ready
    setTimeout(() => {
        initializeDashboard();
    }, 500);

    console.log('✅ ARIA platform ready!');
});

// Export new functions
window.startCourse = startCourse;
window.updateProgress = updateProgress;
window.scheduleNetworking = scheduleNetworking;
window.reviewPortfolio = reviewPortfolio;
window.viewAllAchievements = viewAllAchievements;
window.showAchievementModal = showAchievementModal;
window.closeAchievementModal = closeAchievementModal;
window.continueWithARIA = continueWithARIA;

// Step 3: Courses, Certifications and Portfolio Platforms
// Data for Coursera courses specifically for Andrii's transformation
const courseraCoursesData = [
    {
        id: 1,
        title: "Machine Learning Specialization",
        instructor: "Andrew Ng",
        university: "Stanford University",
        duration: "11 weeks",
        level: "Intermediate",
        rating: 4.9,
        enrolled: 2800000,
        price: "Free to audit, $49/month for certificate",
        relevance: 98,
        status: "recommended",
        description: "Master machine learning fundamentals with the world's leading expert. Perfect for engineers transitioning to AI.",
        skills: ["Machine Learning", "Python", "Neural Networks", "Deep Learning"],
        projects: ["Linear Regression", "Neural Networks", "Recommender Systems"],
        timeToComplete: "3-4 months",
        nextSession: "15 January 2025",
        manufacturingRelevance: "Ideal for quality control automation and predictive maintenance in SMT/CBA processes"
    },
    {
        id: 2,
        title: "Google Data Analytics Professional Certificate",
        instructor: "Google Career Certificates",
        university: "Google",
        duration: "6 months",
        level: "Beginner",
        rating: 4.6,
        enrolled: 500000,
        price: "$49/month",
        relevance: 94,
        status: "in-progress",
        description: "Complete data analytics program with hands-on projects and job-ready skills.",
        skills: ["Data Analysis", "SQL", "Tableau", "R Programming"],
        projects: ["Data Cleaning", "Visualization Dashboard", "Case Study Analysis"],
        timeToComplete: "4-6 months",
        nextSession: "Available now",
        manufacturingRelevance: "Essential for analyzing manufacturing data and creating KPI dashboards"
    },
    {
        id: 3,
        title: "IBM Data Science Professional Certificate",
        instructor: "IBM Skills Network",
        university: "IBM",
        duration: "10 months",
        level: "Intermediate",
        rating: 4.5,
        enrolled: 300000,
        price: "$39/month",
        relevance: 91,
        status: "planned",
        description: "Comprehensive data science program with Python, machine learning, and data visualization.",
        skills: ["Python", "Data Science", "Machine Learning", "Data Visualization"],
        projects: ["Capstone Project", "ML Model Deployment", "Data Pipeline"],
        timeToComplete: "8-10 months",
        nextSession: "1 February 2025",
        manufacturingRelevance: "Perfect for implementing data science solutions in manufacturing environments"
    },
    {
        id: 4,
        title: "Digital Manufacturing & Design Technology",
        instructor: "University at Buffalo",
        university: "University at Buffalo",
        duration: "12 weeks",
        level: "Advanced",
        rating: 4.6,
        enrolled: 45000,
        price: "$79/month",
        relevance: 95,
        status: "available",
        description: "Explore Industry 4.0 technologies and digital transformation in manufacturing.",
        skills: ["Industry 4.0", "Digital Manufacturing", "IoT", "Automation"],
        projects: ["Smart Factory Design", "IoT Implementation", "Digital Twin"],
        timeToComplete: "3-4 months",
        nextSession: "20 January 2025",
        manufacturingRelevance: "Directly applicable to current SMT/CBA role and future AI integration"
    },
    {
        id: 5,
        title: "Applied Data Science with Python Specialization",
        instructor: "University of Michigan",
        university: "University of Michigan",
        duration: "5 months",
        level: "Intermediate",
        rating: 4.4,
        enrolled: 180000,
        price: "$49/month",
        relevance: 89,
        status: "available",
        description: "Learn data science through hands-on Python programming and real-world projects.",
        skills: ["Python", "Pandas", "Matplotlib", "Scikit-learn"],
        projects: ["Data Analysis", "Machine Learning", "Text Mining"],
        timeToComplete: "4-5 months",
        nextSession: "1 February 2025",
        manufacturingRelevance: "Essential Python skills for manufacturing data analysis and automation"
    }
];

// Premium certifications data
const premiumCertificationsData = [
    {
        id: 1,
        name: "AWS Certified Machine Learning - Specialty",
        provider: "Amazon Web Services",
        cost: 300,
        duration: "3-4 months prep",
        difficulty: "Advanced",
        roi: 4.0,
        salaryIncrease: "€15,000 - €25,000",
        validityPeriod: "3 years",
        prerequisites: ["AWS Cloud Practitioner", "Python experience"],
        examFormat: "Multiple choice, 180 minutes",
        passingScore: "750/1000",
        relevance: 96,
        status: "recommended",
        description: "Industry-leading ML certification with highest ROI for manufacturing professionals transitioning to AI.",
        keyTopics: ["ML Implementation", "Data Engineering", "Modeling", "Operations"],
        manufacturingValue: "Essential for implementing ML solutions in manufacturing environments",
        nextExamDate: "Available year-round",
        studyResources: ["AWS Training", "Practice Exams", "Hands-on Labs"]
    },
    {
        id: 2,
        name: "Microsoft Azure AI Engineer Associate",
        provider: "Microsoft",
        cost: 165,
        duration: "2-3 months prep",
        difficulty: "Intermediate",
        roi: 3.5,
        salaryIncrease: "€12,000 - €20,000",
        validityPeriod: "2 years",
        prerequisites: ["Azure Fundamentals", "Programming experience"],
        examFormat: "Multiple choice + labs, 150 minutes",
        passingScore: "700/1000",
        relevance: 92,
        status: "planned",
        description: "Microsoft's premier AI certification, excellent for enterprise environments.",
        keyTopics: ["Cognitive Services", "ML Solutions", "Computer Vision", "NLP"],
        manufacturingValue: "Perfect for integrating AI into existing Microsoft-based manufacturing systems",
        nextExamDate: "Available year-round",
        studyResources: ["Microsoft Learn", "Practice Labs", "Documentation"]
    },
    {
        id: 3,
        name: "Udacity AI for Manufacturing Nanodegree",
        provider: "Udacity",
        cost: 1600,
        duration: "4 months",
        difficulty: "Advanced",
        roi: 2.8,
        salaryIncrease: "€18,000 - €30,000",
        validityPeriod: "Lifetime",
        prerequisites: ["Python", "Statistics", "Manufacturing experience"],
        examFormat: "Project-based assessment",
        passingScore: "Pass/Fail based on projects",
        relevance: 98,
        status: "considering",
        description: "Specialized program combining AI with manufacturing expertise - perfect for your background.",
        keyTopics: ["Predictive Maintenance", "Quality Control AI", "Process Optimization", "Computer Vision"],
        manufacturingValue: "Directly applicable to SMT/CBA processes and PVD technology optimization",
        nextExamDate: "Monthly cohorts",
        studyResources: ["Mentor Support", "Real Projects", "Industry Partnerships"]
    },
    {
        id: 4,
        name: "Stanford AI Professional Certificate",
        provider: "Stanford University",
        cost: 4500,
        duration: "9 months",
        difficulty: "Expert",
        roi: 3.2,
        salaryIncrease: "€25,000 - €40,000",
        validityPeriod: "Lifetime",
        prerequisites: ["Advanced Math", "Programming", "ML Fundamentals"],
        examFormat: "Capstone project + presentations",
        passingScore: "B+ or higher",
        relevance: 94,
        status: "future-goal",
        description: "Prestigious certificate from world's leading AI university. Ultimate career accelerator.",
        keyTopics: ["Advanced ML", "AI Ethics", "Research Methods", "Innovation"],
        manufacturingValue: "Positions you as thought leader in AI-driven manufacturing transformation",
        nextExamDate: "September 2025 cohort",
        studyResources: ["Stanford Faculty", "Research Projects", "Industry Connections"]
    }
];

// Portfolio platforms data
const portfolioPlatformsData = [
    {
        id: 1,
        name: "GitHub Pages",
        type: "Code Repository + Website",
        cost: "Free",
        difficulty: "Beginner",
        setupTime: "2-3 hours",
        visibility: "Public",
        customDomain: "Yes (free)",
        relevance: 95,
        status: "priority",
        description: "Essential for showcasing code projects and building professional developer presence.",
        features: ["Version Control", "Project Showcase", "Professional Website", "Collaboration"],
        manufacturingProjects: [
            "SMT Quality Control Computer Vision",
            "PVD Process Optimization ML",
            "Manufacturing Data Pipeline",
            "Predictive Maintenance Dashboard"
        ],
        advantages: ["Industry Standard", "Free", "Professional", "SEO Friendly"],
        nextSteps: ["Create Account", "Setup Repository", "Build First Project", "Custom Domain"]
    },
    {
        id: 2,
        name: "Kaggle Notebooks",
        type: "Data Science Platform",
        cost: "Free",
        difficulty: "Intermediate",
        setupTime: "1 hour",
        visibility: "Public",
        customDomain: "No",
        relevance: 92,
        status: "recommended",
        description: "World's largest data science community. Perfect for showcasing ML and data analysis skills.",
        features: ["Competitions", "Datasets", "Community", "GPU Access"],
        manufacturingProjects: [
            "Manufacturing Defect Detection",
            "Process Parameter Optimization",
            "Quality Prediction Models",
            "Equipment Failure Analysis"
        ],
        advantages: ["Large Community", "Real Datasets", "Competitions", "Recognition"],
        nextSteps: ["Create Profile", "Join Competition", "Publish Notebook", "Build Reputation"]
    },
    {
        id: 3,
        name: "DataSciencePortfol.io",
        type: "Specialized Portfolio",
        cost: "$15/month",
        difficulty: "Beginner",
        setupTime: "4-5 hours",
        visibility: "Public",
        customDomain: "Yes",
        relevance: 88,
        status: "considering",
        description: "Specialized platform for data science professionals with beautiful templates.",
        features: ["Templates", "Project Showcase", "Blog", "Analytics"],
        manufacturingProjects: [
            "Manufacturing Analytics Dashboard",
            "AI-Driven Quality Control",
            "Process Improvement Case Studies",
            "Industry 4.0 Implementation"
        ],
        advantages: ["Professional Templates", "Easy Setup", "Analytics", "SEO Optimized"],
        nextSteps: ["Choose Template", "Upload Projects", "Write Case Studies", "Optimize SEO"]
    },
    {
        id: 4,
        name: "Personal Website (Custom)",
        type: "Professional Website",
        cost: "$50-100/year",
        difficulty: "Advanced",
        setupTime: "20-30 hours",
        visibility: "Public",
        customDomain: "Yes",
        relevance: 90,
        status: "future-goal",
        description: "Ultimate professional presence with complete control and customization.",
        features: ["Full Control", "Custom Design", "Blog", "Contact Forms"],
        manufacturingProjects: [
            "Complete Portfolio Showcase",
            "Technical Blog",
            "Case Study Library",
            "Professional Services"
        ],
        advantages: ["Complete Control", "Professional", "Scalable", "Personal Brand"],
        nextSteps: ["Domain Registration", "Hosting Setup", "Design Development", "Content Creation"]
    }
];

// Initialize sections data
function initializeSectionsData() {
    populateCoursesSection();
    populateCertificationsSection();
    populatePortfolioSection();
    console.log('📚 Sections data initialized');
}

// Populate courses section
function populateCoursesSection() {
    const coursesGrid = document.getElementById('coursesGrid');
    if (!coursesGrid) return;

    coursesGrid.innerHTML = courseraCoursesData.map(course => `
        <div class="course-card ${course.status}" data-course-id="${course.id}">
            <div class="course-status-badge ${course.status}">
                ${getStatusLabel(course.status)}
            </div>
            <div class="course-header">
                <h3>${course.title}</h3>
                <div class="course-meta">
                    <span class="instructor">${course.instructor}</span>
                    <span class="university">${course.university}</span>
                </div>
            </div>

            <div class="course-stats">
                <div class="stat">
                    <i class="fas fa-star"></i>
                    <span>${course.rating}</span>
                </div>
                <div class="stat">
                    <i class="fas fa-users"></i>
                    <span>${formatNumber(course.enrolled)}</span>
                </div>
                <div class="stat">
                    <i class="fas fa-clock"></i>
                    <span>${course.duration}</span>
                </div>
                <div class="stat">
                    <i class="fas fa-signal"></i>
                    <span>${course.level}</span>
                </div>
            </div>

            <div class="course-description">
                <p>${course.description}</p>
            </div>

            <div class="course-relevance">
                <span class="relevance-label">Relevance for Andrii:</span>
                <div class="relevance-bar">
                    <div class="relevance-fill" style="width: ${course.relevance}%"></div>
                </div>
                <span class="relevance-value">${course.relevance}%</span>
            </div>

            <div class="course-skills">
                ${course.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
            </div>

            <div class="manufacturing-insight">
                <h4><i class="fas fa-industry"></i> Manufacturing Relevance</h4>
                <p>${course.manufacturingRelevance}</p>
            </div>

            <div class="course-actions">
                <button class="course-btn primary" onclick="enrollInCourse(${course.id})">
                    <i class="fas ${getActionIcon(course.status)}"></i>
                    ${getActionLabel(course.status)}
                </button>
                <button class="course-btn secondary" onclick="viewCourseDetails(${course.id})">
                    <i class="fas fa-info-circle"></i>
                    Details
                </button>
                <button class="course-btn tertiary" onclick="scheduleCourse(${course.id})">
                    <i class="fas fa-calendar"></i>
                    Schedule
                </button>
            </div>
        </div>
    `).join('');
}

// Populate certifications section
function populateCertificationsSection() {
    const certificationsGrid = document.getElementById('certificationsGrid');
    if (!certificationsGrid) return;

    certificationsGrid.innerHTML = premiumCertificationsData.map(cert => `
        <div class="certification-card ${cert.status}" data-cert-id="${cert.id}">
            <div class="cert-header">
                <div class="cert-provider-logo">
                    <i class="fab fa-${getProviderIcon(cert.provider)}"></i>
                </div>
                <div class="cert-info">
                    <h3>${cert.name}</h3>
                    <p class="cert-provider">${cert.provider}</p>
                </div>
                <div class="cert-cost">
                    <span class="cost-label">Cost:</span>
                    <span class="cost-value">$${cert.cost}</span>
                </div>
            </div>

            <div class="cert-metrics">
                <div class="metric">
                    <span class="metric-label">ROI:</span>
                    <span class="metric-value roi-${cert.roi >= 3.5 ? 'high' : cert.roi >= 3 ? 'medium' : 'low'}">${cert.roi}x</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Salary Increase:</span>
                    <span class="metric-value">${cert.salaryIncrease}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Prep Time:</span>
                    <span class="metric-value">${cert.duration}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Difficulty:</span>
                    <span class="metric-value difficulty-${cert.difficulty.toLowerCase()}">${cert.difficulty}</span>
                </div>
            </div>

            <div class="cert-description">
                <p>${cert.description}</p>
            </div>

            <div class="manufacturing-value">
                <h4><i class="fas fa-industry"></i> Manufacturing Value</h4>
                <p>${cert.manufacturingValue}</p>
            </div>

            <div class="cert-topics">
                <h4>Key Topics:</h4>
                <div class="topics-list">
                    ${cert.keyTopics.map(topic => `<span class="topic-tag">${topic}</span>`).join('')}
                </div>
            </div>

            <div class="cert-actions">
                <button class="cert-btn primary" onclick="startCertPrep(${cert.id})">
                    <i class="fas fa-play"></i>
                    Start Preparation
                </button>
                <button class="cert-btn secondary" onclick="viewCertDetails(${cert.id})">
                    <i class="fas fa-info-circle"></i>
                    Full Details
                </button>
                <button class="cert-btn tertiary" onclick="compareCertifications(${cert.id})">
                    <i class="fas fa-balance-scale"></i>
                    Compare
                </button>
            </div>
        </div>
    `).join('');
}

// Populate portfolio section
function populatePortfolioSection() {
    const portfolioGrid = document.getElementById('portfolioGrid');
    if (!portfolioGrid) return;

    portfolioGrid.innerHTML = portfolioPlatformsData.map(platform => `
        <div class="portfolio-card ${platform.status}" data-platform-id="${platform.id}">
            <div class="platform-header">
                <div class="platform-icon">
                    <i class="fab fa-${getPlatformIcon(platform.name)}"></i>
                </div>
                <div class="platform-info">
                    <h3>${platform.name}</h3>
                    <p class="platform-type">${platform.type}</p>
                </div>
                <div class="platform-cost ${platform.cost === 'Free' ? 'free' : 'paid'}">
                    ${platform.cost}
                </div>
            </div>

            <div class="platform-stats">
                <div class="stat">
                    <span class="stat-label">Setup Time:</span>
                    <span class="stat-value">${platform.setupTime}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Difficulty:</span>
                    <span class="stat-value difficulty-${platform.difficulty.toLowerCase()}">${platform.difficulty}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Relevance:</span>
                    <span class="stat-value">${platform.relevance}%</span>
                </div>
            </div>

            <div class="platform-description">
                <p>${platform.description}</p>
            </div>

            <div class="manufacturing-projects">
                <h4><i class="fas fa-project-diagram"></i> Manufacturing Projects</h4>
                <ul class="projects-list">
                    ${platform.manufacturingProjects.map(project => `<li>${project}</li>`).join('')}
                </ul>
            </div>

            <div class="platform-advantages">
                <h4>Key Advantages:</h4>
                <div class="advantages-list">
                    ${platform.advantages.map(advantage => `<span class="advantage-tag">${advantage}</span>`).join('')}
                </div>
            </div>

            <div class="platform-actions">
                <button class="platform-btn primary" onclick="createAccount(${platform.id})">
                    <i class="fas fa-user-plus"></i>
                    Create Account
                </button>
                <button class="platform-btn secondary" onclick="startProject(${platform.id})">
                    <i class="fas fa-rocket"></i>
                    Start Project
                </button>
                <button class="platform-btn tertiary" onclick="viewExamples(${platform.id})">
                    <i class="fas fa-eye"></i>
                    View Examples
                </button>
            </div>
        </div>
    `).join('');
}

// Helper functions
function getStatusLabel(status) {
    const labels = {
        'recommended': 'Polecany',
        'in-progress': 'W trakcie',
        'planned': 'Zaplanowany',
        'available': 'Dostępny',
        'considering': 'Rozważany',
        'future-goal': 'Przyszły cel',
        'priority': 'Priorytet'
    };
    return labels[status] || status;
}

function getActionIcon(status) {
    const icons = {
        'recommended': 'fa-play',
        'in-progress': 'fa-continue',
        'planned': 'fa-calendar-plus',
        'available': 'fa-play',
        'considering': 'fa-eye',
        'future-goal': 'fa-bookmark',
        'priority': 'fa-rocket'
    };
    return icons[status] || 'fa-play';
}

function getActionLabel(status) {
    const labels = {
        'recommended': 'Rozpocznij',
        'in-progress': 'Kontynuuj',
        'planned': 'Zaplanuj',
        'available': 'Zapisz się',
        'considering': 'Sprawdź',
        'future-goal': 'Dodaj do celów',
        'priority': 'Rozpocznij teraz'
    };
    return labels[status] || 'Rozpocznij';
}

function getProviderIcon(provider) {
    const icons = {
        'Amazon Web Services': 'aws',
        'Microsoft': 'microsoft',
        'Udacity': 'graduation-cap',
        'Stanford University': 'university'
    };
    return icons[provider] || 'certificate';
}

function getPlatformIcon(platform) {
    const icons = {
        'GitHub Pages': 'github',
        'Kaggle Notebooks': 'kaggle',
        'DataSciencePortfol.io': 'chart-bar',
        'Personal Website (Custom)': 'globe'
    };
    return icons[platform] || 'laptop-code';
}

function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(0) + 'k';
    }
    return num.toString();
}

// Update initialization to include sections
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    // Initialize dashboard and sections after a short delay
    setTimeout(() => {
        initializeDashboard();
        initializeSectionsData();
    }, 500);

    console.log('✅ ARIA platform ready!');
});

// Step 3: Button handlers for courses, certifications and portfolio
// Course action handlers
function enrollInCourse(courseId) {
    const course = courseraCoursesData.find(c => c.id === courseId);
    if (!course) return;

    console.log(`📚 Enrolling in course: ${course.title}`);

    // Update course status
    course.status = 'in-progress';
    userProgress.completedCourses++;
    saveUserProgress();

    // Add achievement
    const achievement = {
        id: Date.now(),
        title: `Enrolled in ${course.title}`,
        description: `Started learning with ${course.instructor}`,
        date: new Date().toISOString(),
        impact: `+${Math.floor(Math.random() * 5 + 3)}% ${course.skills[0]} Skills`,
        icon: "fas fa-graduation-cap"
    };

    userProgress.achievements.push(achievement);
    saveUserProgress();

    // ARIA response
    addARIAMessage(`Świetnie! Zapisałem Cię na "${course.title}" z ${course.instructor}. To doskonały wybór dla Twojej transformacji! Kurs ma ${course.relevance}% relevance dla Twojego profilu. Czy chcesz, żebym pomógł Ci zaplanować harmonogram nauki?`);

    showQuickReplies([
        "Zaplanuj harmonogram",
        "Pokaż materiały",
        "Ustaw przypomnienia",
        "Inne kursy"
    ]);

    // Refresh the courses section
    populateCoursesSection();

    showNotification(`Zapisano na kurs: ${course.title}`, 'success');

    // Show achievement modal
    setTimeout(() => {
        showAchievementModal(achievement);
    }, 1500);
}

function viewCourseDetails(courseId) {
    const course = courseraCoursesData.find(c => c.id === courseId);
    if (!course) return;

    console.log(`📖 Viewing details for: ${course.title}`);

    addARIAMessage(`Oto szczegóły kursu "${course.title}":

📊 **Statystyki:**
- Instruktor: ${course.instructor} (${course.university})
- Ocena: ${course.rating}/5 (${formatNumber(course.enrolled)} uczestników)
- Czas trwania: ${course.duration}
- Poziom: ${course.level}
- Cena: ${course.price}

🎯 **Relevance dla Ciebie: ${course.relevance}%**

🔧 **Manufacturing Relevance:**
${course.manufacturingRelevance}

📚 **Umiejętności:** ${course.skills.join(', ')}

🚀 **Projekty:** ${course.projects.join(', ')}

Czy chcesz się zapisać na ten kurs?`);

    showQuickReplies([
        "Zapisz mnie",
        "Porównaj z innymi",
        "Zaplanuj na później",
        "Więcej informacji"
    ]);

    showNotification('Szczegóły kursu wyświetlone w czacie ARIA', 'info');
}

function scheduleCourse(courseId) {
    const course = courseraCoursesData.find(c => c.id === courseId);
    if (!course) return;

    console.log(`📅 Scheduling course: ${course.title}`);

    course.status = 'planned';
    populateCoursesSection();

    addARIAMessage(`Zaplanowałem kurs "${course.title}" na ${course.nextSession}.

📅 **Harmonogram:**
- Start: ${course.nextSession}
- Czas ukończenia: ${course.timeToComplete}
- Intensywność: ${Math.ceil(parseInt(course.duration) / 4)} godzin/tydzień

Czy chcesz, żebym ustawił przypomnienia i przygotował plan nauki?`);

    showQuickReplies([
        "Ustaw przypomnienia",
        "Przygotuj plan nauki",
        "Dodaj do kalendarza",
        "Zmień datę"
    ]);

    showNotification(`Kurs zaplanowany na ${course.nextSession}`, 'success');
}

// Certification action handlers
function startCertPrep(certId) {
    const cert = premiumCertificationsData.find(c => c.id === certId);
    if (!cert) return;

    console.log(`🎓 Starting certification prep: ${cert.name}`);

    cert.status = 'in-progress';
    populateCertificationsSection();

    addARIAMessage(`Rozpoczynamy przygotowania do ${cert.name}!

💰 **Inwestycja:** $${cert.cost} (ROI: ${cert.roi}x)
📈 **Potencjalny wzrost wynagrodzeń:** ${cert.salaryIncrease}
⏱️ **Czas przygotowań:** ${cert.duration}
🎯 **Poziom trudności:** ${cert.difficulty}

**Plan przygotowań:**
1. ${cert.studyResources[0]}
2. ${cert.studyResources[1]}
3. ${cert.studyResources[2]}

**Manufacturing Value:** ${cert.manufacturingValue}

Czy chcesz, żebym stworzył szczegółowy plan nauki?`);

    showQuickReplies([
        "Stwórz plan nauki",
        "Pokaż materiały",
        "Zaplanuj egzamin",
        "Porównaj z innymi"
    ]);

    showNotification(`Rozpoczęto przygotowania: ${cert.name}`, 'success');
}

function viewCertDetails(certId) {
    const cert = premiumCertificationsData.find(c => c.id === certId);
    if (!cert) return;

    console.log(`📋 Viewing certification details: ${cert.name}`);

    addARIAMessage(`Szczegółowe informacje o ${cert.name}:

🏢 **Provider:** ${cert.provider}
💰 **Koszt:** $${cert.cost}
📊 **ROI:** ${cert.roi}x (${cert.salaryIncrease})
⏰ **Przygotowania:** ${cert.duration}
🎯 **Trudność:** ${cert.difficulty}
📅 **Ważność:** ${cert.validityPeriod}

**Wymagania wstępne:**
${cert.prerequisites.map(req => `• ${req}`).join('\n')}

**Format egzaminu:**
${cert.examFormat}
Wynik zaliczający: ${cert.passingScore}

**Kluczowe tematy:**
${cert.keyTopics.map(topic => `• ${topic}`).join('\n')}

**Wartość dla manufacturing:**
${cert.manufacturingValue}

**Następny termin egzaminu:** ${cert.nextExamDate}

Czy chcesz rozpocząć przygotowania?`);

    showQuickReplies([
        "Rozpocznij przygotowania",
        "Zaplanuj egzamin",
        "Porównaj certyfikacje",
        "Sprawdź wymagania"
    ]);

    showNotification('Szczegóły certyfikacji w czacie ARIA', 'info');
}

function compareCertifications(certId) {
    const cert = premiumCertificationsData.find(c => c.id === certId);
    if (!cert) return;

    console.log(`⚖️ Comparing certifications with: ${cert.name}`);

    // Find top 3 certifications by ROI
    const topCerts = premiumCertificationsData
        .sort((a, b) => b.roi - a.roi)
        .slice(0, 3);

    addARIAMessage(`Porównanie certyfikacji dla Twojego profilu:

**${cert.name}** (wybrana):
• ROI: ${cert.roi}x | Koszt: $${cert.cost} | Trudność: ${cert.difficulty}
• Wzrost wynagrodzeń: ${cert.salaryIncrease}

**Porównanie z top certyfikacjami:**

${topCerts.map((c, index) => `
${index + 1}. **${c.name}**
• ROI: ${c.roi}x | Koszt: $${c.cost} | Trudność: ${c.difficulty}
• Wzrost: ${c.salaryIncrease}
• Relevance: ${c.relevance}%`).join('\n')}

**Rekomendacja ARIA:**
Na podstawie Twojego profilu w manufacturing i celów w AI, ${topCerts[0].name} ma najlepszy ROI (${topCerts[0].roi}x) i jest idealny dla Twojej transformacji kariery.

Którą certyfikację chcesz wybrać?`);

    showQuickReplies([
        topCerts[0].name.split(' ')[0],
        topCerts[1].name.split(' ')[0],
        topCerts[2].name.split(' ')[0],
        "Więcej informacji"
    ]);

    showNotification('Porównanie certyfikacji w czacie ARIA', 'info');
}

// Portfolio platform action handlers
function createAccount(platformId) {
    const platform = portfolioPlatformsData.find(p => p.id === platformId);
    if (!platform) return;

    console.log(`👤 Creating account on: ${platform.name}`);

    platform.status = 'in-progress';
    populatePortfolioSection();

    addARIAMessage(`Świetnie! Tworzymy konto na ${platform.name}!

🎯 **Platforma:** ${platform.type}
💰 **Koszt:** ${platform.cost}
⏱️ **Czas setup:** ${platform.setupTime}
🎨 **Trudność:** ${platform.difficulty}
📊 **Relevance:** ${platform.relevance}%

**Zalety:**
${platform.advantages.map(adv => `• ${adv}`).join('\n')}

**Projekty manufacturing dla Twojego portfolio:**
${platform.manufacturingProjects.map(proj => `• ${proj}`).join('\n')}

**Następne kroki:**
${platform.nextSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}

Czy chcesz, żebym pomógł Ci z pierwszym projektem?`);

    showQuickReplies([
        "Pomóż z pierwszym projektem",
        "Pokaż przykłady",
        "Setup instrukcje",
        "Inne platformy"
    ]);

    showNotification(`Konto tworzone: ${platform.name}`, 'success');
}

function startProject(platformId) {
    const platform = portfolioPlatformsData.find(p => p.id === platformId);
    if (!platform) return;

    console.log(`🚀 Starting project on: ${platform.name}`);

    const projectSuggestions = [
        {
            title: "SMT Quality Control Computer Vision",
            description: "AI system for automated defect detection in SMT assembly",
            technologies: ["Python", "OpenCV", "TensorFlow", "Flask"],
            impact: "60% reduction in false positives",
            timeline: "4-6 weeks"
        },
        {
            title: "PVD Process Optimization ML",
            description: "Machine learning model for optimizing PVD coating parameters",
            technologies: ["Python", "Scikit-learn", "Pandas", "Matplotlib"],
            impact: "15% improvement in coating quality",
            timeline: "3-4 weeks"
        },
        {
            title: "Manufacturing Data Pipeline",
            description: "Automated data pipeline for real-time manufacturing analytics",
            technologies: ["Python", "Apache Airflow", "PostgreSQL", "Grafana"],
            impact: "Real-time KPI monitoring",
            timeline: "5-7 weeks"
        }
    ];

    const randomProject = projectSuggestions[Math.floor(Math.random() * projectSuggestions.length)];

    addARIAMessage(`Rozpoczynamy projekt na ${platform.name}!

🎯 **Polecany projekt:** ${randomProject.title}

📝 **Opis:** ${randomProject.description}

🛠️ **Technologie:** ${randomProject.technologies.join(', ')}

📈 **Oczekiwany impact:** ${randomProject.impact}

⏰ **Timeline:** ${randomProject.timeline}

**Dlaczego ten projekt?**
• Wykorzystuje Twoje doświadczenie w ${randomProject.title.includes('SMT') ? 'SMT/CBA' : randomProject.title.includes('PVD') ? 'PVD technology' : 'manufacturing'}
• Pokazuje umiejętności AI/ML
• Ma realny business impact
• Idealny dla portfolio

Czy chcesz rozpocząć ten projekt?`);

    showQuickReplies([
        "Rozpocznij projekt",
        "Inne pomysły",
        "Pokaż template",
        "Pomoc z setup"
    ]);

    showNotification(`Projekt rozpoczęty: ${randomProject.title}`, 'success');
}

function viewExamples(platformId) {
    const platform = portfolioPlatformsData.find(p => p.id === platformId);
    if (!platform) return;

    console.log(`👁️ Viewing examples for: ${platform.name}`);

    const examples = {
        1: [ // GitHub Pages
            "https://github.com/manufacturing-ai/smt-quality-control",
            "https://github.com/process-optimization/pvd-ml-models",
            "https://github.com/industry40/predictive-maintenance"
        ],
        2: [ // Kaggle
            "Manufacturing Defect Detection Competition",
            "Process Parameter Optimization Challenge",
            "Quality Prediction Models Showcase"
        ],
        3: [ // DataSciencePortfol.io
            "Manufacturing Analytics Dashboard",
            "AI-Driven Quality Control Case Study",
            "Industry 4.0 Implementation Portfolio"
        ],
        4: [ // Personal Website
            "Complete Portfolio Showcase",
            "Technical Blog with Case Studies",
            "Professional Services Landing Page"
        ]
    };

    addARIAMessage(`Przykłady portfolio na ${platform.name}:

${examples[platformId].map((example, index) => `${index + 1}. ${example}`).join('\n')}

**Dlaczego te przykłady są dobre:**
• Pokazują real-world manufacturing problems
• Demonstrują AI/ML solutions
• Mają measurable business impact
• Są relevant dla Twojego background

**Kluczowe elementy successful portfolio:**
• Clear problem statement
• Technical solution approach
• Quantified results
• Professional presentation
• Manufacturing domain expertise

Czy chcesz, żebym pomógł Ci stworzyć podobny projekt?`);

    showQuickReplies([
        "Stwórz podobny projekt",
        "Analizuj przykłady",
        "Template projektu",
        "Inne platformy"
    ]);

    showNotification(`Przykłady wyświetlone dla ${platform.name}`, 'info');
}

// Export new functions
window.enrollInCourse = enrollInCourse;
window.viewCourseDetails = viewCourseDetails;
window.scheduleCourse = scheduleCourse;
window.startCertPrep = startCertPrep;
window.viewCertDetails = viewCertDetails;
window.compareCertifications = compareCertifications;
window.createAccount = createAccount;
window.startProject = startProject;
window.viewExamples = viewExamples;

console.log('📝 Step 3: Courses, certifications and portfolio platforms with button handlers loaded');
