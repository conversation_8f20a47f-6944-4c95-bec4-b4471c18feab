// AI Coach ARIA - Step 1: Basic Navigation and Initialization
// Enhanced Career Transformation Platform for <PERSON><PERSON><PERSON>

// Global state management
let currentSection = 'dashboard';
let ariaConversationHistory = [];
let userProgress = {
    careerProgress: 35,
    completedCourses: 2,
    totalCourses: 5,
    achievements: [],
    currentGoals: []
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    console.log('✅ ARIA platform ready!');
});

// Initialize particle background
function initializeParticles() {
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            particles: {
                number: { value: 60, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.4, random: false },
                size: { value: 3, random: true },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: "#ffffff",
                    opacity: 0.3,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 4,
                    direction: "none",
                    random: false,
                    straight: false,
                    out_mode: "out",
                    bounce: false
                }
            },
            interactivity: {
                detect_on: "canvas",
                events: {
                    onhover: { enable: true, mode: "repulse" },
                    onclick: { enable: true, mode: "push" },
                    resize: true
                },
                modes: {
                    repulse: { distance: 100, duration: 0.4 },
                    push: { particles_nb: 2 }
                }
            },
            retina_detect: true
        });
    }
}

// Setup navigation functionality
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.section');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSection = this.getAttribute('href').substring(1);

            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');

            // Show target section
            sections.forEach(section => {
                section.classList.remove('active');
                if (section.id === targetSection) {
                    section.classList.add('active');
                }
            });

            currentSection = targetSection;

            // Notify ARIA about section change
            notifyARIASectionChange(targetSection);

            console.log(`📍 Navigated to: ${targetSection}`);
        });
    });
}

// Initialize ARIA chat system
function initializeARIA() {
    console.log('🤖 Initializing AI Coach ARIA...');

    // Setup chat input
    const chatInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendButton');
    const voiceButton = document.getElementById('voiceButton');

    if (chatInput && sendButton) {
        // Send message on Enter key
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Send message on button click
        sendButton.addEventListener('click', sendMessage);
    }

    if (voiceButton) {
        voiceButton.addEventListener('click', toggleVoiceInput);
    }

    // Setup voice toggle in navbar
    const voiceToggle = document.getElementById('voiceToggle');
    if (voiceToggle) {
        voiceToggle.addEventListener('click', openVoiceModal);
    }

    // Initialize with welcome message
    setTimeout(() => {
        addARIAMessage("Cześć Andrii! 👋 Jestem ARIA, Twój osobisty AI Coach. Jestem tutaj, aby pomóc Ci w transformacji kariery z Manufacturing Engineering do AI/Data Science. Jak mogę Ci dziś pomóc?");
        showQuickReplies([
            "Pokaż mój postęp",
            "Zaplanuj naukę",
            "Networking events",
            "Aktualizuj portfolio"
        ]);
    }, 1000);
}

// Load user progress from localStorage
function loadUserProgress() {
    const savedProgress = localStorage.getItem('andriiProgress');
    if (savedProgress) {
        userProgress = { ...userProgress, ...JSON.parse(savedProgress) };
    }

    // Initialize with some sample achievements if none exist
    if (userProgress.achievements.length === 0) {
        userProgress.achievements = [
            {
                id: 1,
                title: "Python Fundamentals",
                description: "Ukończono podstawowy kurs Python",
                date: new Date().toISOString(),
                impact: "+10% Data Analysis Skills",
                icon: "fab fa-python"
            },
            {
                id: 2,
                title: "RPA Professional",
                description: "Zdobyto certyfikat SS&C Blue Prism",
                date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                impact: "+15% Automation Skills",
                icon: "fas fa-robot"
            }
        ];
        saveUserProgress();
    }

    console.log('📊 User progress loaded:', userProgress);
}

// Save user progress to localStorage
function saveUserProgress() {
    localStorage.setItem('andriiProgress', JSON.stringify(userProgress));
}

// Send message to ARIA
function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();

    if (message) {
        addUserMessage(message);
        chatInput.value = '';

        // Process message with ARIA
        setTimeout(() => {
            processARIAResponse(message);
        }, 1000);
    }
}

// Add user message to chat
function addUserMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message user';
    messageElement.innerHTML = `
        ${message}
        <div class="message-time">${new Date().toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Add to conversation history
    ariaConversationHistory.push({
        type: 'user',
        message: message,
        timestamp: new Date().toISOString()
    });
}

// Add ARIA message to chat
function addARIAMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message aria';
    messageElement.innerHTML = `
        ${message}
        <div class="message-time">${new Date().toLocaleTimeString('pl-PL', { hour: '2-digit', minute: '2-digit' })}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Add to conversation history
    ariaConversationHistory.push({
        type: 'aria',
        message: message,
        timestamp: new Date().toISOString()
    });
}

// Show quick reply buttons
function showQuickReplies(replies) {
    const quickReplies = document.getElementById('quickReplies');
    quickReplies.innerHTML = '';

    replies.forEach(reply => {
        const button = document.createElement('button');
        button.className = 'quick-reply-btn';
        button.textContent = reply;
        button.addEventListener('click', () => {
            addUserMessage(reply);
            setTimeout(() => {
                processARIAResponse(reply);
            }, 1000);
        });
        quickReplies.appendChild(button);
    });
}

// Process ARIA response based on user input
function processARIAResponse(userMessage) {
    const message = userMessage.toLowerCase();
    let response = "";
    let quickReplies = [];

    if (message.includes('postęp') || message.includes('progress')) {
        response = `Świetnie! Twój obecny postęp w transformacji kariery wynosi ${userProgress.careerProgress}%. Ukończyłeś ${userProgress.completedCourses} z ${userProgress.totalCourses} planowanych kursów. Ostatnio zdobyłeś certyfikat RPA Professional - to doskonały krok w kierunku automatyzacji!`;
        quickReplies = ["Pokaż szczegóły", "Następne kroki", "Aktualizuj postęp"];
    } else if (message.includes('nauka') || message.includes('kurs')) {
        response = "Doskonały pomysł! Na podstawie Twojego profilu w inżynierii materiałowej, polecam skupić się na kursach Machine Learning for Manufacturing. Masz już solidne podstawy w Python - czas przejść do poziomu intermediate!";
        quickReplies = ["Pokaż kursy", "Zaplanuj harmonogram", "Sprawdź certyfikacje"];
    } else if (message.includes('networking') || message.includes('wydarzenia')) {
        response = "Networking to kluczowy element transformacji kariery! W Polsce mamy świetne wydarzenia AI. Najbliższe to Global AI Bootcamp Kraków (7 marca) i miesięczne Trójmiasto AI Meetup. Chcesz, żebym pomógł Ci się przygotować?";
        quickReplies = ["Zobacz wydarzenia", "Przygotuj pitch", "LinkedIn strategy"];
    } else if (message.includes('portfolio')) {
        response = "Twoje portfolio to Twoja wizytówka! Z Twoim doświadczeniem w SMT/CBA i PVD, możesz stworzyć unikalne projekty łączące manufacturing z AI. Polecam zacząć od Computer Vision dla Quality Control - to będzie hit!";
        quickReplies = ["Pomysły na projekty", "GitHub setup", "Portfolio review"];
    } else {
        response = "Rozumiem! Jestem tutaj, aby pomóc Ci w każdym aspekcie transformacji kariery. Czy chcesz porozmawiać o konkretnym obszarze rozwoju?";
        quickReplies = ["Kursy i certyfikacje", "Networking", "Portfolio", "Planowanie kariery"];
    }

    addARIAMessage(response);
    showQuickReplies(quickReplies);
}

// Notify ARIA about section changes
function notifyARIASectionChange(section) {
    let contextMessage = "";

    switch(section) {
        case 'dashboard':
            contextMessage = "Widzę, że przeglądasz dashboard. Twój postęp wygląda świetnie! Czy chcesz omówić jakiś konkretny aspekt?";
            break;
        case 'courses':
            contextMessage = "Świetnie, że sprawdzasz kursy Coursera! Te 5 certyfikacji to doskonały plan dla Twojej transformacji. Którym chcesz zacząć?";
            break;
        case 'certifications':
            contextMessage = "Certyfikacje premium to inwestycja w przyszłość! AWS ML Specialty ma najlepszy ROI dla Twojego profilu. Chcesz omówić strategię?";
            break;
        case 'portfolio':
            contextMessage = "Portfolio to Twoja siła! Z doświadczeniem w manufacturing możesz stworzyć projekty, których nikt inny nie ma. Pomogę Ci je zaplanować!";
            break;
        case 'networking':
            contextMessage = "Networking w AI community to klucz do sukcesu! Wydarzenia w Polsce są świetne. Chcesz, żebym pomógł Ci się przygotować?";
            break;
        case 'roadmap':
            contextMessage = "Mapa kariery pokazuje Twoją drogę do sukcesu! 12 miesięcy to realny czas na transformację. Omówimy najbliższe kroki?";
            break;
    }

    if (contextMessage) {
        setTimeout(() => {
            addARIAMessage(contextMessage);
            showQuickReplies(["Tak, pomóż mi", "Pokaż szczegóły", "Inne pytanie"]);
        }, 2000);
    }
}

// Voice input functionality
function toggleVoiceInput() {
    console.log('🎤 Voice input toggled');
    // This will be implemented in next step
    showNotification('Funkcja głosowa będzie dostępna wkrótce!', 'info');
}

function openVoiceModal() {
    const voiceModal = document.getElementById('voiceModal');
    if (voiceModal) {
        voiceModal.style.display = 'block';
    }
}

function closeVoiceModal() {
    const voiceModal = document.getElementById('voiceModal');
    if (voiceModal) {
        voiceModal.style.display = 'none';
    }
}

// Utility function for notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Export functions for global access
window.sendMessage = sendMessage;
window.toggleVoiceInput = toggleVoiceInput;
window.openVoiceModal = openVoiceModal;
window.closeVoiceModal = closeVoiceModal;

// Step 2: Dashboard Charts and Action Buttons
// Initialize dashboard when DOM is ready
function initializeDashboard() {
    console.log('📊 Initializing dashboard charts...');

    createProgressGauge();
    createSkillsRadar();
    createSalaryChart();
    populateAchievements();
    populateTimelinePreview();

    console.log('✅ Dashboard initialized');
}

// Create progress gauge chart
function createProgressGauge() {
    const ctx = document.getElementById('progressGauge');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [userProgress.careerProgress, 100 - userProgress.careerProgress],
                backgroundColor: [
                    'linear-gradient(45deg, #4a90e2, #667eea)',
                    '#e9ecef'
                ],
                borderWidth: 0,
                cutout: '75%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: { enabled: false }
            }
        }
    });
}

// Create skills radar chart
function createSkillsRadar() {
    const ctx = document.getElementById('skillsRadar');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: [
                'Materials Science',
                'Python Programming',
                'Data Analysis',
                'Machine Learning',
                'Quality Engineering',
                'Business Intelligence',
                'Leadership',
                'Manufacturing'
            ],
            datasets: [{
                label: 'Obecny poziom',
                data: [95, 70, 65, 60, 90, 45, 70, 95],
                backgroundColor: 'rgba(74, 144, 226, 0.2)',
                borderColor: '#4a90e2',
                borderWidth: 2,
                pointBackgroundColor: '#4a90e2'
            }, {
                label: 'Poziom docelowy',
                data: [98, 90, 88, 85, 95, 80, 85, 98],
                backgroundColor: 'rgba(255, 107, 107, 0.2)',
                borderColor: '#ff6b6b',
                borderWidth: 2,
                pointBackgroundColor: '#ff6b6b'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20,
                        display: false
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    pointLabels: {
                        font: { size: 11 },
                        color: '#666'
                    }
                }
            }
        }
    });
}

// Create salary projection chart
function createSalaryChart() {
    const ctx = document.getElementById('salaryChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Obecnie', '6 miesięcy', '1 rok', '18 miesięcy', '2 lata'],
            datasets: [{
                label: 'Projekcja wynagrodzeń (EUR)',
                data: [70000, 85000, 105000, 140000, 160000],
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#4a90e2',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    min: 60000,
                    max: 180000,
                    ticks: {
                        callback: function(value) {
                            return '€' + (value / 1000) + 'k';
                        },
                        color: '#666',
                        font: { size: 11 }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#666',
                        font: { size: 11 }
                    },
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Populate achievements list
function populateAchievements() {
    const achievementsList = document.getElementById('achievementsList');
    if (!achievementsList) return;

    achievementsList.innerHTML = '';

    // Show last 3 achievements
    const recentAchievements = userProgress.achievements.slice(-3);

    recentAchievements.forEach(achievement => {
        const achievementElement = document.createElement('div');
        achievementElement.className = 'achievement-item';
        achievementElement.innerHTML = `
            <div class="achievement-icon">
                <i class="${achievement.icon}"></i>
            </div>
            <div class="achievement-content">
                <h4>${achievement.title}</h4>
                <p>${achievement.description}</p>
                <small>${achievement.impact}</small>
            </div>
        `;

        achievementElement.addEventListener('click', () => {
            showAchievementModal(achievement);
        });

        achievementsList.appendChild(achievementElement);
    });
}

// Populate timeline preview
function populateTimelinePreview() {
    const timelinePreview = document.getElementById('timelinePreview');
    if (!timelinePreview) return;

    const upcomingMilestones = [
        {
            date: '15 Sty',
            title: 'Machine Learning Specialization',
            description: 'Rozpoczęcie kursu Andrew Ng na Coursera'
        },
        {
            date: '1 Lut',
            title: 'Portfolio Project #1',
            description: 'Computer Vision dla SMT Quality Control'
        },
        {
            date: '15 Lut',
            title: 'AWS ML Specialty',
            description: 'Przygotowania do egzaminu certyfikacyjnego'
        }
    ];

    timelinePreview.innerHTML = '';

    upcomingMilestones.forEach(milestone => {
        const milestoneElement = document.createElement('div');
        milestoneElement.className = 'timeline-item';
        milestoneElement.innerHTML = `
            <div class="timeline-date">${milestone.date}</div>
            <div class="timeline-content">
                <h4>${milestone.title}</h4>
                <p>${milestone.description}</p>
            </div>
        `;
        timelinePreview.appendChild(milestoneElement);
    });
}

// Action button functions
function startCourse() {
    console.log('🎓 Starting course...');

    addARIAMessage("Świetnie! Widzę, że chcesz rozpocząć naukę. Na podstawie Twojego profilu polecam zacząć od Machine Learning Specialization Andrew Ng. To idealny kurs dla inżynierów przechodzących do AI!");

    showQuickReplies([
        "Zapisz mnie na kurs",
        "Pokaż harmonogram",
        "Inne opcje"
    ]);

    // Navigate to courses section
    setTimeout(() => {
        document.querySelector('[href="#courses"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji kursów...', 'info');
}

function updateProgress() {
    console.log('📈 Updating progress...');

    // Simulate progress update
    userProgress.careerProgress = Math.min(userProgress.careerProgress + 5, 100);
    saveUserProgress();

    // Add new achievement
    const newAchievement = {
        id: Date.now(),
        title: "Postęp Zaktualizowany",
        description: "Ręczna aktualizacja postępu kariery",
        date: new Date().toISOString(),
        impact: "+5% Career Progress",
        icon: "fas fa-chart-line"
    };

    userProgress.achievements.push(newAchievement);
    saveUserProgress();

    // Refresh dashboard
    populateAchievements();
    createProgressGauge();

    addARIAMessage(`Doskonale! Zaktualizowałem Twój postęp do ${userProgress.careerProgress}%. Każdy krok przybliża Cię do celu! 🎯`);

    showQuickReplies([
        "Pokaż szczegóły",
        "Następne kroki",
        "Zaplanuj naukę"
    ]);

    showNotification('Postęp zaktualizowany!', 'success');

    // Show achievement modal
    setTimeout(() => {
        showAchievementModal(newAchievement);
    }, 1500);
}

function scheduleNetworking() {
    console.log('👥 Scheduling networking...');

    addARIAMessage("Networking to klucz do sukcesu w AI! Zaplanujmy Twoje uczestnictwo w wydarzeniach. Polecam zacząć od Global AI Bootcamp Kraków - to świetna okazja do poznania community!");

    showQuickReplies([
        "Zapisz na wydarzenie",
        "Przygotuj elevator pitch",
        "LinkedIn strategy"
    ]);

    // Navigate to networking section
    setTimeout(() => {
        document.querySelector('[href="#networking"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji networking...', 'info');
}

function reviewPortfolio() {
    console.log('📁 Reviewing portfolio...');

    addARIAMessage("Twoje portfolio to Twoja wizytówka! Z doświadczeniem w SMT/CBA i PVD masz unikalne możliwości. Stwórzmy projekty, które pokażą Twoją ekspertyzę w manufacturing + AI!");

    showQuickReplies([
        "Pomysły na projekty",
        "GitHub optimization",
        "Portfolio review"
    ]);

    // Navigate to portfolio section
    setTimeout(() => {
        document.querySelector('[href="#portfolio"]').click();
    }, 2000);

    showNotification('Przechodzę do sekcji portfolio...', 'info');
}

function viewAllAchievements() {
    console.log('🏆 Viewing all achievements...');

    addARIAMessage(`Masz już ${userProgress.achievements.length} osiągnięć! To świetny postęp. Każde osiągnięcie przybliża Cię do celu transformacji kariery. Które z nich było dla Ciebie najważniejsze?`);

    const achievementTitles = userProgress.achievements.map(a => a.title).slice(-4);
    showQuickReplies(achievementTitles);

    showNotification(`Masz ${userProgress.achievements.length} osiągnięć!`, 'success');
}

// Achievement modal functions
function showAchievementModal(achievement) {
    const modal = document.getElementById('achievementModal');
    const details = document.getElementById('achievementDetails');
    const impact = document.getElementById('achievementImpact');
    const nextSteps = document.getElementById('nextSteps');

    if (!modal || !details || !impact || !nextSteps) return;

    details.innerHTML = `
        <div class="achievement-showcase">
            <div class="achievement-icon-large">
                <i class="${achievement.icon}"></i>
            </div>
            <h3>${achievement.title}</h3>
            <p>${achievement.description}</p>
            <div class="achievement-date">
                Osiągnięte: ${new Date(achievement.date).toLocaleDateString('pl-PL')}
            </div>
        </div>
    `;

    impact.innerHTML = `
        <h4><i class="fas fa-chart-line"></i> Wpływ na Rozwój</h4>
        <div class="impact-item">
            <span class="impact-label">Bezpośredni wpływ:</span>
            <span class="impact-value">${achievement.impact}</span>
        </div>
        <div class="impact-item">
            <span class="impact-label">Postęp kariery:</span>
            <span class="impact-value">+${Math.floor(Math.random() * 3 + 2)}% ogólny postęp</span>
        </div>
        <div class="impact-item">
            <span class="impact-label">Wartość rynkowa:</span>
            <span class="impact-value">+€${Math.floor(Math.random() * 5000 + 2000)} potencjał zarobków</span>
        </div>
    `;

    nextSteps.innerHTML = `
        <h4><i class="fas fa-arrow-right"></i> Następne Kroki</h4>
        <ul class="next-steps-list">
            <li>Zaktualizuj profil LinkedIn z nowym osiągnięciem</li>
            <li>Dodaj projekt do portfolio GitHub</li>
            <li>Podziel się sukcesem w AI community</li>
            <li>Zaplanuj następny milestone</li>
        </ul>
    `;

    modal.style.display = 'block';
}

function closeAchievementModal() {
    const modal = document.getElementById('achievementModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function continueWithARIA() {
    closeAchievementModal();
    addARIAMessage("Gratulacje ponownie! 🎉 Jestem dumny z Twojego postępu. Czy chcesz omówić następne kroki w Twojej transformacji kariery?");
    showQuickReplies([
        "Zaplanuj następny cel",
        "Aktualizuj LinkedIn",
        "Nowy projekt portfolio"
    ]);
}

// Update the initialization to include dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI Coach ARIA initializing...');

    initializeParticles();
    setupNavigation();
    initializeARIA();
    loadUserProgress();

    // Initialize dashboard after a short delay to ensure DOM is ready
    setTimeout(() => {
        initializeDashboard();
    }, 500);

    console.log('✅ ARIA platform ready!');
});

// Export new functions
window.startCourse = startCourse;
window.updateProgress = updateProgress;
window.scheduleNetworking = scheduleNetworking;
window.reviewPortfolio = reviewPortfolio;
window.viewAllAchievements = viewAllAchievements;
window.showAchievementModal = showAchievementModal;
window.closeAchievementModal = closeAchievementModal;
window.continueWithARIA = continueWithARIA;

console.log('📝 Step 2: Dashboard charts and action buttons loaded');
